import type { SortOptions } from '@server/api/themoviedb';
import { SortOptionsIterable } from '@server/api/themoviedb';
import type {
  TmdbGenre,
  TmdbSearchMovieResponse,
  TmdbSearchTvResponse,
} from '@server/api/themoviedb/interfaces';
import { MediaType } from '@server/constants/media';
import { Blacklist } from '@server/entity/Blacklist';
import { getSettings } from '@server/lib/settings';
import logger from '@server/logger';
import { createTmdbWithRegionLanguage } from '@server/routes/discover';
import type { EntityManager } from 'typeorm';

export const TMDB_API_DELAY_MS = 250;

export class AbortTransaction extends Error {}

/**
 * Configuration for processing different types of blacklist items
 */
export interface BlacklistProcessingConfig {
  mediaType: MediaType;
  ids: string[];
  pageLimit: number;
  blacklistField: keyof Pick<
    Blacklist,
    'blacklistedTags' | 'blacklistedGenresMovies' | 'blacklistedGenresTvShows'
  >;
  validateId: (id: string, tmdb: any) => Promise<boolean>;
  getDiscoverFunction: (
    tmdb: any
  ) => (
    options: any
  ) => Promise<TmdbSearchMovieResponse | TmdbSearchTvResponse>;
  getDiscoverParams: (id: string) => { keywords?: string; genre?: string };
  logLabel: string;
  itemType: 'keyword' | 'genre';
}

/**
 * Validates a keyword ID by checking if it exists in TMDB
 */
export async function validateKeyword(
  keywordId: string,
  tmdb: any
): Promise<boolean> {
  try {
    const keywordDetails = await tmdb.getKeywordDetails({
      keywordId: Number(keywordId),
    });
    return keywordDetails !== null;
  } catch (error) {
    return false;
  }
}

/**
 * Validates a genre ID by checking if it exists in the available genres for the media type
 */
export async function validateGenre(
  genreId: string,
  tmdb: any,
  mediaType: MediaType
): Promise<boolean> {
  try {
    const availableGenres: TmdbGenre[] =
      mediaType === MediaType.MOVIE
        ? await tmdb.getMovieGenres()
        : await tmdb.getTvGenres();

    return availableGenres.some((g) => g.id === Number(genreId));
  } catch (error) {
    return false;
  }
}

/**
 * Processes blacklist items for a specific configuration
 */
export async function processBlacklistItems(
  config: BlacklistProcessingConfig,
  em: EntityManager,
  onProgress: () => void,
  checkRunning: () => boolean
): Promise<Set<string>> {
  const tmdb = createTmdbWithRegionLanguage();
  const invalidIds = new Set<string>();

  for (const id of config.ids) {
    // Validate the ID first
    const isValid = await config.validateId(id, tmdb);

    if (!isValid) {
      logger.warn(
        `Skipping invalid ${config.itemType} in blacklisted ${config.itemType}s`,
        {
          label: config.logLabel,
          [`${config.itemType}Id`]: id,
          mediaType: config.mediaType,
        }
      );
      invalidIds.add(id);
      continue;
    }

    let queryMax = config.pageLimit * SortOptionsIterable.length;
    let fixedSortMode = false;

    for (let query = 0; query < queryMax; query++) {
      const page: number = fixedSortMode
        ? query + 1
        : (query % config.pageLimit) + 1;
      const sortBy: SortOptions | undefined = fixedSortMode
        ? undefined
        : SortOptionsIterable[query % SortOptionsIterable.length];

      if (!checkRunning()) {
        throw new AbortTransaction();
      }

      try {
        const discoverParams = {
          page,
          sortBy,
          ...config.getDiscoverParams(id),
        };

        const response = await config.getDiscoverFunction(tmdb)(discoverParams);

        await processResults(
          response,
          id,
          config.mediaType,
          config.blacklistField,
          em
        );
        await new Promise((res) => setTimeout(res, TMDB_API_DELAY_MS));

        onProgress();

        if (page === 1 && response.total_pages <= queryMax) {
          // We will finish the item with fewer queries than expected, move progress accordingly
          const remainingQueries = queryMax - response.total_pages;
          for (let i = 0; i < remainingQueries; i++) {
            onProgress();
          }
          fixedSortMode = true;
          queryMax = response.total_pages;
        }
      } catch (error) {
        logger.error(
          `Error processing ${config.itemType} in blacklisted ${config.itemType}s`,
          {
            label: config.logLabel,
            [`${config.itemType}Id`]: id,
            mediaType: config.mediaType,
            errorMessage: error.message,
          }
        );
      }
    }
  }

  return invalidIds;
}

/**
 * Processes the results from a TMDB discover query and updates the blacklist
 */
async function processResults(
  response: TmdbSearchMovieResponse | TmdbSearchTvResponse,
  itemId: string,
  mediaType: MediaType,
  blacklistField: keyof Pick<
    Blacklist,
    'blacklistedTags' | 'blacklistedGenresMovies' | 'blacklistedGenresTvShows'
  >,
  em: EntityManager
): Promise<void> {
  const blacklistRepository = em.getRepository(Blacklist);

  for (const entry of response.results) {
    const blacklistEntry = await blacklistRepository.findOne({
      where: { tmdbId: entry.id },
    });

    if (blacklistEntry) {
      // Update existing blacklist entry
      const currentValue = blacklistEntry[blacklistField];
      if (currentValue && !currentValue.includes(`,${itemId},`)) {
        await blacklistRepository.update(blacklistEntry.id, {
          [blacklistField]: `${currentValue}${itemId},`,
        });
      } else if (!currentValue) {
        await blacklistRepository.update(blacklistEntry.id, {
          [blacklistField]: `,${itemId},`,
        });
      }
    } else {
      // Create new blacklist entry
      const blacklistRequest: {
        mediaType: MediaType;
        title: string;
        tmdbId: number;
        blacklistedTags?: string;
        blacklistedGenresMovies?: string;
        blacklistedGenresTvShows?: string;
      } = {
        mediaType,
        title: 'title' in entry ? entry.title : entry.name,
        tmdbId: entry.id,
      };

      blacklistRequest[blacklistField] = `,${itemId},`;

      await Blacklist.addToBlacklist(
        {
          blacklistRequest,
        },
        em
      );
    }
  }
}

/**
 * Calculates the total number of operations for progress tracking
 */
export function calculateTotalOperations(
  configs: BlacklistProcessingConfig[]
): number {
  return configs.reduce((total, config) => {
    return (
      total + config.ids.length * config.pageLimit * SortOptionsIterable.length
    );
  }, 0);
}

/**
 * Cleans up invalid IDs from settings
 */
export async function cleanupInvalidIds(
  invalidIds: Set<string>,
  originalIds: string[],
  settingKey: string,
  logLabel: string,
  itemType: 'keyword' | 'genre',
  mediaType?: MediaType
): Promise<void> {
  if (invalidIds.size === 0) {
    return;
  }

  const settings = getSettings();
  const currentIds = originalIds.filter((id) => !invalidIds.has(id));
  const cleanedIds = currentIds.join(',');
  const originalValue = (settings.main as any)[settingKey];

  if (cleanedIds !== originalValue) {
    (settings.main as any)[settingKey] = cleanedIds;
    await settings.save();

    logger.info(`Cleaned up invalid ${itemType}s from settings`, {
      label: logLabel,
      mediaType: mediaType,
      [`removed${itemType.charAt(0).toUpperCase() + itemType.slice(1)}s`]:
        Array.from(invalidIds),
      [`newBlacklisted${
        itemType.charAt(0).toUpperCase() + itemType.slice(1)
      }s`]: cleanedIds,
    });
  }
}

/**
 * Creates a configuration for processing tags
 */
export function createTagsConfig(
  tags: string[],
  pageLimit: number
): BlacklistProcessingConfig[] {
  return [
    {
      mediaType: MediaType.MOVIE,
      ids: tags,
      pageLimit,
      blacklistField: 'blacklistedTags',
      validateId: validateKeyword,
      getDiscoverFunction: (tmdb) => tmdb.getDiscoverMovies,
      getDiscoverParams: (id) => ({ keywords: id }),
      logLabel: 'Blacklisted Tags Processor',
      itemType: 'keyword',
    },
    {
      mediaType: MediaType.TV,
      ids: tags,
      pageLimit,
      blacklistField: 'blacklistedTags',
      validateId: validateKeyword,
      getDiscoverFunction: (tmdb) => tmdb.getDiscoverTv,
      getDiscoverParams: (id) => ({ keywords: id }),
      logLabel: 'Blacklisted Tags Processor',
      itemType: 'keyword',
    },
  ];
}

/**
 * Creates configurations for processing genres
 */
export function createGenresConfigs(
  movieGenres: string[],
  moviePageLimit: number,
  tvGenres: string[],
  tvPageLimit: number
): BlacklistProcessingConfig[] {
  const configs: BlacklistProcessingConfig[] = [];

  if (movieGenres.length > 0) {
    configs.push({
      mediaType: MediaType.MOVIE,
      ids: movieGenres,
      pageLimit: moviePageLimit,
      blacklistField: 'blacklistedGenresMovies',
      validateId: (id, tmdb) => validateGenre(id, tmdb, MediaType.MOVIE),
      getDiscoverFunction: (tmdb) => tmdb.getDiscoverMovies,
      getDiscoverParams: (id) => ({ genre: id }),
      logLabel: 'Blacklisted Genres Processor',
      itemType: 'genre',
    });
  }

  if (tvGenres.length > 0) {
    configs.push({
      mediaType: MediaType.TV,
      ids: tvGenres,
      pageLimit: tvPageLimit,
      blacklistField: 'blacklistedGenresTvShows',
      validateId: (id, tmdb) => validateGenre(id, tmdb, MediaType.TV),
      getDiscoverFunction: (tmdb) => tmdb.getDiscoverTv,
      getDiscoverParams: (id) => ({ genre: id }),
      logLabel: 'Blacklisted Genres Processor',
      itemType: 'genre',
    });
  }

  return configs;
}

/**
 * Parses comma-separated IDs and filters out empty values
 */
export function parseIds(idsString: string): string[] {
  return idsString
    .split(',')
    .map((id) => id.trim())
    .filter((id) => id.length > 0);
}
