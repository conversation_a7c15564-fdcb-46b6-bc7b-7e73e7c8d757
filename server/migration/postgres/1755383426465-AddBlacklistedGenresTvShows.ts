import type { MigrationInterface, QueryRunner } from 'typeorm';

export class AddBlacklistedGenresTvShows1755383426465
  implements MigrationInterface
{
  name = 'AddBlacklistedGenresTvShows1755383426465';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "blacklist" ADD "blacklistedGenresMovies" character varying`
    );
    await queryRunner.query(
      `ALTER TABLE "blacklist" ADD "blacklistedGenresTvShows" character varying`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "blacklist" DROP COLUMN "blacklistedGenresTvShows"`
    );
    await queryRunner.query(
      `ALTER TABLE "blacklist" DROP COLUMN "blacklistedGenresMovies"`
    );
  }
}
